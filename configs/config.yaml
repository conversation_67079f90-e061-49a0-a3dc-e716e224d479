# 作品展示网站 - 简化配置文件

# 服务器配置
server:
  port: 8080
  mode: debug  # 开发环境用debug，生产环境改为release

# 数据库配置（选择一种）
database:
  # SQLite配置（推荐个人使用，零配置）
  driver: sqlite
  sqlite:
    path: ./data/portfolio.db
  
  # MySQL配置（如果需要使用MySQL，请取消注释并配置）
  # driver: mysql
  # mysql:
  #   host: localhost
  #   port: 3306
  #   username: root
  #   password: your_password
  #   database: portfolio_db

# 管理员密码配置
admin:
  password: admin123  # 请务必修改默认密码！

# 文件上传配置
upload:
  max_size: 10485760  # 最大文件大小 10MB
  allowed_types:      # 允许的文件类型
    - .jpg
    - .jpeg
    - .png
    - .gif
    - .webp
  storage_path: ./uploads
  url_prefix: /uploads

# 会话配置（用于管理员登录）
session:
  secret: "your-session-secret-please-change-this-in-production"
  expire_hours: 24

# 基础安全配置
security:
  # 密码要求
  password_min_length: 6
  
  # 登录限制
  max_login_attempts: 5
  lockout_minutes: 15

# 日志配置
log:
  level: info
  file_path: ./logs/app.log

# 分页配置
pagination:
  page_size: 12  # 首页每页显示作品数量

# 缓存配置（可选，提升性能）
cache:
  enabled: false  # 如果安装了Redis可以设为true
  # redis:
  #   host: localhost
  #   port: 6379

# 网站信息配置
site:
  title: "作品展示"
  description: "作品展示网站"
  keywords: "作品,展示,设计"

# 开发环境配置
development:
  # 是否显示详细错误信息
  show_errors: true
  # 是否启用热重载
  hot_reload: false

# 生产环境配置（部署时使用）
production:
  # 启用Gzip压缩
  enable_gzip: true
  # 静态文件缓存时间（秒）
  static_cache_time: 86400  # 1天
  # 隐藏服务器信息
  hide_server_header: true
