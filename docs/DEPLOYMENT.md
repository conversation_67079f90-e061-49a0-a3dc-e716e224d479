# 部署文档

## 部署架构

### 生产环境架构图
```
                    ┌─────────────┐
                    │   用户端    │
                    └─────────────┘
                           │
                    ┌─────────────┐
                    │  Cloudflare │ (CDN + SSL)
                    └─────────────┘
                           │
                    ┌─────────────┐
                    │    Nginx    │ (反向代理)
                    └─────────────┘
                           │
              ┌─────────────────────────┐
              │                         │
       ┌─────────────┐           ┌─────────────┐
       │ Go App (1)  │           │ Go App (2)  │ (负载均衡)
       └─────────────┘           └─────────────┘
              │                         │
              └─────────────────────────┘
                           │
              ┌─────────────────────────┐
              │                         │
       ┌─────────────┐           ┌─────────────┐
       │   MySQL     │           │    Redis    │
       │  (Master)   │           │   (Cache)   │
       └─────────────┘           └─────────────┘
              │
       ┌─────────────┐
       │   MySQL     │
       │  (Slave)    │ (读写分离)
       └─────────────┘
```

## Docker部署

### 1. Dockerfile

```dockerfile
# 多阶段构建
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装依赖
RUN apk add --no-cache git

# 复制go mod文件
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main cmd/server/main.go

# 运行阶段
FROM alpine:latest

# 安装ca证书
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

WORKDIR /root/

# 从构建阶段复制二进制文件
COPY --from=builder /app/main .
COPY --from=builder /app/web ./web
COPY --from=builder /app/configs ./configs

# 创建上传目录
RUN mkdir -p uploads

# 暴露端口
EXPOSE 8080

# 运行应用
CMD ["./main"]
```

### 2. docker-compose.yml

```yaml
version: '3.8'

services:
  # 应用服务
  app:
    build: .
    container_name: portfolio-app
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=release
      - DB_DRIVER=mysql  # 或者 sqlite
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=portfolio_user
      - DB_PASSWORD=secure_password
      - DB_NAME=portfolio_db
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./uploads:/root/uploads
      - ./configs:/root/configs
    depends_on:
      - mysql
      - redis
    networks:
      - portfolio-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: portfolio-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: portfolio_db
      MYSQL_USER: portfolio_user
      MYSQL_PASSWORD: secure_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    networks:
      - portfolio-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: portfolio-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - portfolio-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: portfolio-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - ./uploads:/var/www/uploads
    depends_on:
      - app
    networks:
      - portfolio-network

volumes:
  mysql_data:
  redis_data:

networks:
  portfolio-network:
    driver: bridge
```

### 3. Nginx配置

**nginx/conf.d/portfolio.conf**:
```nginx
upstream portfolio_backend {
    server app:8080;
    # 如果有多个应用实例
    # server app2:8080;
}

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL配置
    ssl_certificate /etc/nginx/ssl/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 文件上传大小限制
    client_max_body_size 10M;

    # 静态文件服务
    location /uploads/ {
        alias /var/www/uploads/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    location /static/ {
        proxy_pass http://portfolio_backend;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    # API代理
    location /api/ {
        proxy_pass http://portfolio_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 主应用代理
    location / {
        proxy_pass http://portfolio_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

## 云服务器部署

### 1. 服务器要求

**最低配置**:
- CPU: 2核
- 内存: 4GB
- 存储: 40GB SSD
- 带宽: 5Mbps

**推荐配置**:
- CPU: 4核
- 内存: 8GB
- 存储: 100GB SSD
- 带宽: 10Mbps

### 2. 环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 创建项目目录
sudo mkdir -p /opt/portfolio-website
cd /opt/portfolio-website
```

### 3. 部署脚本

**scripts/deploy.sh**:
```bash
#!/bin/bash

set -e

PROJECT_DIR="/opt/portfolio-website"
BACKUP_DIR="/backup/portfolio"
DATE=$(date +%Y%m%d_%H%M%S)

echo "开始部署作品展示网站..."

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份当前版本
if [ -d "$PROJECT_DIR" ]; then
    echo "备份当前版本..."
    tar -czf $BACKUP_DIR/portfolio_backup_$DATE.tar.gz -C $PROJECT_DIR .
fi

# 拉取最新代码
echo "拉取最新代码..."
cd $PROJECT_DIR
git pull origin main

# 构建并启动服务
echo "构建并启动服务..."
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 30

# 健康检查
echo "进行健康检查..."
if curl -f http://localhost:8080/api/health; then
    echo "部署成功！"
else
    echo "部署失败，回滚到上一版本..."
    docker-compose down
    # 这里可以添加回滚逻辑
    exit 1
fi

# 清理旧的备份文件（保留最近7天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "部署完成！"
```

### 4. 自动化部署 (GitHub Actions)

**.github/workflows/deploy.yml**:
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.21

    - name: Run tests
      run: go test ./...

    - name: Build application
      run: go build -o main cmd/server/main.go

    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /opt/portfolio-website
          git pull origin main
          docker-compose down
          docker-compose build --no-cache
          docker-compose up -d
```

## 监控和日志

### 1. 应用监控

**docker-compose.monitoring.yml**:
```yaml
version: '3.8'

services:
  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: portfolio-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - portfolio-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: portfolio-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
    networks:
      - portfolio-network

volumes:
  prometheus_data:
  grafana_data:
```

### 2. 日志管理

**docker-compose.logging.yml**:
```yaml
version: '3.8'

services:
  # ELK Stack for logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: portfolio-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - portfolio-network

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: portfolio-logstash
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch
    networks:
      - portfolio-network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: portfolio-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - portfolio-network

volumes:
  elasticsearch_data:
```

## 安全配置

### 1. 防火墙设置

```bash
# 安装ufw
sudo apt install ufw

# 默认策略
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 启用防火墙
sudo ufw enable
```

### 2. SSL证书配置

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. 数据库安全

```bash
# MySQL安全配置
docker exec -it portfolio-mysql mysql_secure_installation

# 创建专用数据库用户
docker exec -it portfolio-mysql mysql -u root -p
```

```sql
-- 创建应用专用用户
CREATE USER 'portfolio_user'@'%' IDENTIFIED BY 'secure_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON portfolio_db.* TO 'portfolio_user'@'%';
FLUSH PRIVILEGES;
```

## 备份和恢复

### 1. 自动备份脚本

**scripts/backup.sh**:
```bash
#!/bin/bash

BACKUP_DIR="/backup/portfolio"
DATE=$(date +%Y%m%d_%H%M%S)
MYSQL_CONTAINER="portfolio-mysql"
REDIS_CONTAINER="portfolio-redis"

# 创建备份目录
mkdir -p $BACKUP_DIR/{mysql,redis,uploads}

# 备份MySQL数据库
docker exec $MYSQL_CONTAINER mysqldump -u root -proot_password portfolio_db > $BACKUP_DIR/mysql/portfolio_db_$DATE.sql

# 备份Redis数据
docker exec $REDIS_CONTAINER redis-cli BGSAVE
docker cp $REDIS_CONTAINER:/data/dump.rdb $BACKUP_DIR/redis/dump_$DATE.rdb

# 备份上传文件
tar -czf $BACKUP_DIR/uploads/uploads_$DATE.tar.gz uploads/

# 压缩所有备份
tar -czf $BACKUP_DIR/full_backup_$DATE.tar.gz -C $BACKUP_DIR mysql redis uploads

# 清理临时文件
rm -rf $BACKUP_DIR/mysql/portfolio_db_$DATE.sql
rm -rf $BACKUP_DIR/redis/dump_$DATE.rdb
rm -rf $BACKUP_DIR/uploads/uploads_$DATE.tar.gz

# 删除7天前的备份
find $BACKUP_DIR -name "full_backup_*.tar.gz" -mtime +7 -delete

echo "备份完成: full_backup_$DATE.tar.gz"
```

### 2. 恢复脚本

**scripts/restore.sh**:
```bash
#!/bin/bash

if [ $# -eq 0 ]; then
    echo "使用方法: $0 <backup_file>"
    exit 1
fi

BACKUP_FILE=$1
RESTORE_DIR="/tmp/restore_$(date +%s)"

# 解压备份文件
mkdir -p $RESTORE_DIR
tar -xzf $BACKUP_FILE -C $RESTORE_DIR

# 恢复MySQL数据库
if [ -f "$RESTORE_DIR/mysql/portfolio_db_*.sql" ]; then
    echo "恢复MySQL数据库..."
    docker exec -i portfolio-mysql mysql -u root -proot_password portfolio_db < $RESTORE_DIR/mysql/portfolio_db_*.sql
fi

# 恢复Redis数据
if [ -f "$RESTORE_DIR/redis/dump_*.rdb" ]; then
    echo "恢复Redis数据..."
    docker stop portfolio-redis
    docker cp $RESTORE_DIR/redis/dump_*.rdb portfolio-redis:/data/dump.rdb
    docker start portfolio-redis
fi

# 恢复上传文件
if [ -f "$RESTORE_DIR/uploads/uploads_*.tar.gz" ]; then
    echo "恢复上传文件..."
    tar -xzf $RESTORE_DIR/uploads/uploads_*.tar.gz
fi

# 清理临时文件
rm -rf $RESTORE_DIR

echo "恢复完成！"
```

## 性能优化

### 1. 数据库优化

```sql
-- MySQL配置优化
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
query_cache_size = 128M
max_connections = 200
```

### 2. Redis优化

```conf
# Redis配置优化
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 3. 应用优化

- 启用Gzip压缩
- 配置静态资源缓存
- 使用CDN加速
- 数据库连接池优化
- 图片压缩和WebP格式支持

这个部署文档提供了完整的生产环境部署方案，包括Docker容器化、监控、安全、备份等各个方面，确保应用的稳定运行。
