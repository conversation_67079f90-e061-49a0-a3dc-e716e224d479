# 开发文档

## 🎯 项目概述

这是一个基于Go开发的个人作品展示网站，采用简洁的架构设计，易于理解和扩展。

### 技术选型
- **后端**: Go + Gin框架
- **数据库**: SQLite（默认）/ MySQL
- **前端**: HTML + CSS + JavaScript + Bootstrap
- **认证**: JWT Token

## 🚀 开发环境搭建

### 1. 环境要求
- Go 1.19+
- Git
- 代码编辑器（推荐VS Code）

### 2. 项目初始化
```bash
# 克隆项目
git clone <repository-url>
cd portfolio-website

# 安装依赖
go mod download

# 创建开发目录
mkdir -p data uploads logs

# 复制配置文件
cp configs/config.simple.yaml configs/config.yaml

# 启动开发服务器
go run cmd/server/main.go
```

### 3. 开发工具推荐
```bash
# 安装热重载工具
go install github.com/cosmtrek/air@latest

# 使用air启动（自动重载）
air

# 代码格式化
go fmt ./...

# 代码检查
go vet ./...
```

## 📁 项目结构

```
portfolio-website/
├── cmd/server/main.go          # 程序入口
├── internal/                   # 核心代码
│   ├── config/                 # 配置管理
│   ├── handler/                # HTTP处理器
│   ├── middleware/             # 中间件
│   ├── model/                  # 数据模型
│   ├── service/                # 业务逻辑
│   └── utils/                  # 工具函数
├── web/                        # 前端资源
│   ├── static/                 # CSS/JS/图片
│   └── templates/              # HTML模板
├── configs/config.yaml         # 配置文件
├── data/                       # SQLite数据库
├── uploads/                    # 上传的图片
└── logs/                       # 日志文件
```

### 核心模块说明

#### internal/model/ - 数据模型
- `user.go` - 用户模型
- `portfolio.go` - 作品模型  
- `category.go` - 分类模型

#### internal/handler/ - HTTP处理器
- `web.go` - 前台页面处理
- `api.go` - API接口处理
- `admin.go` - 后台管理处理

#### internal/service/ - 业务逻辑
- `auth.go` - 用户认证
- `portfolio.go` - 作品管理
- `upload.go` - 文件上传

#### internal/middleware/ - 中间件
- `auth.go` - JWT认证
- `cors.go` - 跨域处理
- `logger.go` - 请求日志

## 🗄️ 数据库设计

### 表关系图
```
users (管理员)
  │
  └── portfolios (作品)
        ├── category_id → categories (分类)
        └── portfolio_images (作品图片)
```

### 主要表结构

#### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 分类表 (categories)  
```sql
CREATE TABLE categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. 作品表 (portfolios)
```sql
CREATE TABLE portfolios (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    category_id INTEGER NOT NULL,
    cover_image VARCHAR(500),
    view_count INTEGER DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'published',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);
```

#### 4. 作品图片表 (portfolio_images)
```sql
CREATE TABLE portfolio_images (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    portfolio_id INTEGER NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_name VARCHAR(255),
    sort_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (portfolio_id) REFERENCES portfolios(id)
);
```

## 💻 核心代码示例

### 1. 配置管理
```go
// internal/config/config.go
type Config struct {
    Server   ServerConfig   `yaml:"server"`
    Database DatabaseConfig `yaml:"database"`
    Admin    AdminConfig    `yaml:"admin"`
    Upload   UploadConfig   `yaml:"upload"`
    JWT      JWTConfig      `yaml:"jwt"`
}

// 加载配置
func Load(configPath string) (*Config, error) {
    data, err := os.ReadFile(configPath)
    if err != nil {
        return nil, err
    }
    
    var config Config
    if err := yaml.Unmarshal(data, &config); err != nil {
        return nil, err
    }
    
    return &config, nil
}
```

### 2. 数据模型
```go
// internal/model/portfolio.go
type Portfolio struct {
    ID          uint              `json:"id" gorm:"primaryKey"`
    Title       string            `json:"title" gorm:"size:200;not null"`
    Description string            `json:"description" gorm:"type:text"`
    CategoryID  uint              `json:"category_id" gorm:"not null"`
    Category    Category          `json:"category" gorm:"foreignKey:CategoryID"`
    CoverImage  string            `json:"cover_image" gorm:"size:500"`
    ViewCount   int               `json:"view_count" gorm:"default:0"`
    Status      string            `json:"status" gorm:"size:20;default:published"`
    Images      []PortfolioImage  `json:"images" gorm:"foreignKey:PortfolioID"`
    CreatedAt   time.Time         `json:"created_at"`
    UpdatedAt   time.Time         `json:"updated_at"`
}

type Category struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    Name        string    `json:"name" gorm:"size:100;not null"`
    Description string    `json:"description" gorm:"type:text"`
    SortOrder   int       `json:"sort_order" gorm:"default:0"`
    CreatedAt   time.Time `json:"created_at"`
}
```

### 3. API处理器
```go
// internal/handler/api.go
type APIHandler struct {
    portfolioService service.PortfolioService
}

func (h *APIHandler) GetPortfolios(c *gin.Context) {
    // 默认获取第一页
    page := 1
    pageSize := 12

    portfolios, total, err := h.portfolioService.GetPortfolios(page, pageSize, 0)
    if err != nil {
        c.JSON(500, gin.H{"error": "获取作品失败"})
        return
    }

    c.JSON(200, gin.H{
        "code": 200,
        "data": gin.H{
            "portfolios": portfolios,
            "total":      total,
            "page":       page,
            "page_size":  pageSize,
        },
    })
}

func (h *APIHandler) SearchPortfolios(c *gin.Context) {
    var req struct {
        Keyword  string `json:"keyword"`
        Page     int    `json:"page"`
        PerPage  int    `json:"per_page"`
        Category int    `json:"category_id"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": "参数错误"})
        return
    }

    if req.Page <= 0 {
        req.Page = 1
    }
    if req.PerPage <= 0 {
        req.PerPage = 12
    }

    portfolios, total, err := h.portfolioService.SearchPortfolios(req.Keyword, req.Page, req.PerPage, req.Category)
    if err != nil {
        c.JSON(500, gin.H{"error": "搜索失败"})
        return
    }

    c.JSON(200, gin.H{
        "code": 200,
        "data": gin.H{
            "portfolios": portfolios,
            "total":      total,
            "page":       req.Page,
            "page_size":  req.PerPage,
        },
    })
}
```

### 4. 业务逻辑服务
```go
// internal/service/portfolio.go
type PortfolioService interface {
    GetPortfolios(page, pageSize, categoryID int) ([]*model.Portfolio, int64, error)
    GetPortfolioByID(id uint) (*model.Portfolio, error)
    CreatePortfolio(req *CreatePortfolioRequest) (*model.Portfolio, error)
    IncrementViewCount(id uint) error
}

type portfolioService struct {
    db *gorm.DB
}

func (s *portfolioService) GetPortfolios(page, pageSize, categoryID int) ([]*model.Portfolio, int64, error) {
    var portfolios []*model.Portfolio
    var total int64
    
    query := s.db.Model(&model.Portfolio{}).Where("status = ?", "published")
    if categoryID > 0 {
        query = query.Where("category_id = ?", categoryID)
    }
    
    query.Count(&total)
    
    offset := (page - 1) * pageSize
    err := query.Preload("Category").Preload("Images").
        Order("created_at DESC").
        Limit(pageSize).Offset(offset).
        Find(&portfolios).Error
    
    return portfolios, total, err
}
```

### 5. JWT认证
```go
// internal/utils/jwt.go
type Claims struct {
    UserID   uint   `json:"user_id"`
    Username string `json:"username"`
    jwt.RegisteredClaims
}

func GenerateToken(userID uint, username, secret string) (string, error) {
    claims := Claims{
        UserID:   userID,
        Username: username,
        RegisteredClaims: jwt.RegisteredClaims{
            ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
            IssuedAt:  jwt.NewNumericDate(time.Now()),
            Issuer:    "portfolio-website",
        },
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(secret))
}
```

### 6. 认证中间件
```go
// internal/middleware/auth.go
func AuthMiddleware(jwtSecret string) gin.HandlerFunc {
    return func(c *gin.Context) {
        tokenString := c.GetHeader("Authorization")
        if tokenString == "" {
            c.JSON(401, gin.H{"error": "未提供认证token"})
            c.Abort()
            return
        }
        
        if strings.HasPrefix(tokenString, "Bearer ") {
            tokenString = tokenString[7:]
        }
        
        claims, err := utils.ParseToken(tokenString, jwtSecret)
        if err != nil {
            c.JSON(401, gin.H{"error": "无效的token"})
            c.Abort()
            return
        }
        
        c.Set("user_id", claims.UserID)
        c.Set("username", claims.Username)
        c.Next()
    }
}
```

## 🔧 开发流程

### 1. 添加新功能
```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 在相应模块中添加代码
# - internal/model/ 添加数据模型
# - internal/service/ 添加业务逻辑
# - internal/handler/ 添加HTTP处理器

# 3. 测试功能
go run cmd/server/main.go

# 4. 提交代码
git add .
git commit -m "feat: 添加新功能"
git push origin feature/new-feature
```

### 2. 数据库迁移
```bash
# 创建迁移文件
touch migrations/001_create_tables.sql

# 在迁移文件中添加SQL语句
# 然后运行迁移
go run cmd/migrate/main.go
```

### 3. 前端开发
```bash
# 修改模板文件
vim web/templates/frontend/index.html

# 修改样式文件
vim web/static/css/style.css

# 修改JavaScript文件
vim web/static/js/app.js
```

## 🧪 测试

### 1. 单元测试
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/service

# 运行测试并显示覆盖率
go test -cover ./...
```

### 2. API测试
```bash
# 获取作品列表
curl -X GET "http://localhost:8080/api/portfolios"

# 搜索作品
curl -X POST "http://localhost:8080/api/portfolios/search" \
  -H "Content-Type: application/json" \
  -d '{"keyword":"设计","page":1,"per_page":12}'

# 获取分类下的作品
curl -X GET "http://localhost:8080/api/categories/1/portfolios"

# 测试认证接口
curl -X POST "http://localhost:8080/api/admin/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

## 📝 代码规范

### 1. Go代码规范
- 使用`gofmt`格式化代码
- 遵循Go官方命名规范
- 添加必要的注释
- 错误处理要完整

### 2. 提交规范
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式
- `refactor`: 代码重构
- `test`: 测试相关

### 3. 文件命名
- Go文件使用小写+下划线: `user_service.go`
- 模板文件使用小写+下划线: `portfolio_list.html`
- 静态文件使用小写+连字符: `main-style.css`

## 🚀 部署

### 1. 构建生产版本
```bash
# 构建二进制文件
go build -o portfolio cmd/server/main.go

# 设置生产环境配置
cp configs/config.simple.yaml configs/config.yaml
# 修改配置文件中的生产环境设置

# 启动服务
./portfolio
```

### 2. Docker部署
```bash
# 构建镜像
docker build -t portfolio-website .

# 运行容器
docker run -d -p 8080:8080 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/uploads:/app/uploads \
  portfolio-website
```

## 🔍 调试技巧

### 1. 日志调试
```go
// 在代码中添加日志
log.Printf("Debug: %+v", data)

// 查看日志文件
tail -f logs/app.log
```

### 2. 数据库调试
```bash
# SQLite调试
sqlite3 data/portfolio.db
.tables
.schema portfolios
SELECT * FROM portfolios LIMIT 5;

# MySQL调试
mysql -u root -p
USE portfolio_db;
SHOW TABLES;
DESCRIBE portfolios;
```

## 📚 扩展开发

### 1. 添加新的数据模型
1. 在`internal/model/`中创建新模型
2. 在数据库迁移中添加表结构
3. 在相关服务中添加业务逻辑
4. 在处理器中添加API接口

### 2. 自定义主题
1. 修改`web/templates/`中的HTML模板
2. 更新`web/static/css/`中的样式文件
3. 添加自定义JavaScript功能

### 3. 集成第三方服务
1. 在配置文件中添加第三方服务配置
2. 在`internal/service/`中创建集成服务
3. 在需要的地方调用第三方服务

---

这个开发文档提供了项目的完整开发指南，适合个人项目的开发和维护需求。
```
