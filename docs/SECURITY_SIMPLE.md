# 简单安全指南

## 🔒 基本安全设置（必做）

### 1. 修改默认密码
```yaml
# configs/config.yaml
admin:
  username: admin
  password: your_secure_password  # 改掉默认的admin123
```

### 2. 设置文件权限
```bash
# 配置文件只有你能读写
chmod 640 configs/config.yaml

# 上传目录设置正确权限
chmod 755 uploads/
```

### 3. 文件上传安全
系统已经内置了基本的文件上传安全：
- ✅ 只允许图片格式：jpg, jpeg, png, gif, webp
- ✅ 文件大小限制：10MB
- ✅ 自动检查文件类型
- ✅ 生成安全的文件名

## 🛡️ 生产环境安全（推荐）

### 1. 使用HTTPS
```bash
# 使用Let's Encrypt获取免费SSL证书
sudo apt install certbot
sudo certbot --standalone -d yourdomain.com
```

### 2. 设置防火墙
```bash
# Ubuntu/Debian
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable

# 只允许必要的端口
```

### 3. 定期备份
```bash
#!/bin/bash
# 简单备份脚本
DATE=$(date +%Y%m%d)
tar -czf backup_$DATE.tar.gz data/ uploads/ configs/

# 可以放到crontab中定期执行
# 0 2 * * * /path/to/backup.sh
```

## 🔍 安全检查清单

### 部署前检查
- [ ] 修改了默认管理员密码
- [ ] 设置了正确的文件权限
- [ ] 检查配置文件中没有敏感信息泄露
- [ ] 确认只开放必要的端口

### 运行时检查
```bash
# 检查是否修改了默认密码
grep "admin123" configs/config.yaml
# 如果有输出，说明还在使用默认密码

# 检查文件权限
ls -la configs/config.yaml
# 应该显示 -rw-r----- 或类似权限

# 检查上传目录
ls -la uploads/
# 应该显示 drwxr-xr-x 权限
```

## ⚠️ 常见安全问题

### 1. 密码太简单
❌ 不要用：123456, password, admin123
✅ 建议用：至少8位，包含字母数字和符号

### 2. 文件权限不当
❌ 不要：chmod 777 configs/
✅ 正确：chmod 640 configs/config.yaml

### 3. 暴露敏感信息
❌ 不要：把配置文件提交到公开的Git仓库
✅ 正确：使用 .gitignore 忽略配置文件

## 🚨 发现问题怎么办？

### 如果发现可疑文件上传
1. 立即删除可疑文件
2. 检查 `logs/app.log` 查看上传记录
3. 考虑临时关闭上传功能

### 如果忘记管理员密码
1. 编辑 `configs/config.yaml` 修改密码
2. 重启程序
3. 用新密码登录

### 如果网站被攻击
1. 立即断网或关闭服务
2. 备份当前状态用于分析
3. 检查日志文件
4. 修复问题后再上线

## 🔧 简单的安全加固

### 1. 隐藏服务器信息
```yaml
# configs/config.yaml
production:
  hide_server_header: true
```

### 2. 启用登录限制
```yaml
# configs/config.yaml
security:
  max_login_attempts: 5
  lockout_minutes: 15
```

### 3. 设置日志记录
```yaml
# configs/config.yaml
log:
  level: info
  file_path: ./logs/app.log
```

## 📊 监控建议

### 简单的日志监控
```bash
# 查看最近的访问日志
tail -f logs/app.log

# 查找可疑的登录尝试
grep "login failed" logs/app.log

# 查看文件上传记录
grep "upload" logs/app.log
```

### 系统资源监控
```bash
# 检查磁盘使用
df -h

# 检查内存使用
free -h

# 检查进程状态
ps aux | grep portfolio
```

## 🌐 网络安全

### 如果使用云服务器
1. **修改SSH端口**（不用默认的22）
2. **禁用root登录**
3. **使用密钥登录**而不是密码
4. **设置fail2ban**防止暴力破解

### 如果使用CDN
- 可以隐藏真实服务器IP
- 提供DDoS防护
- 加速静态资源加载

## 📝 安全最佳实践

### 开发环境
- 使用不同的密码
- 可以开启详细日志
- 定期更新依赖

### 生产环境
- 使用强密码
- 关闭调试模式
- 定期备份数据
- 监控异常访问

## 🆘 应急联系

如果遇到严重安全问题：
1. 立即下线网站
2. 保留证据（日志、文件等）
3. 寻求专业帮助
4. 通知相关用户（如果有）

---

**记住：安全是一个持续的过程，不是一次性的设置！**

> 这个简化版安全指南涵盖了个人网站的基本安全需求。如果你需要更高级的安全功能，可以查看完整版安全文档。
