# 数据库设计文档

## 数据库概述

本项目是一个简化的作品展示网站，支持SQLite和MySQL两种数据库。数据库设计简洁高效，专注于作品展示的核心需求。

- **SQLite**: 适合开发环境和小型部署，无需额外配置
- **MySQL**: 适合生产环境和大型应用，需要配置服务器

## 数据库配置

### SQLite配置 (推荐用于开发)
```yaml
database:
  driver: sqlite
  sqlite:
    path: ./data/portfolio.db
    foreign_keys: true
    wal_mode: true
  max_open_conns: 1
  max_idle_conns: 1
  conn_max_lifetime: 3600
```

### MySQL配置 (推荐用于生产)
```yaml
database:
  driver: mysql
  mysql:
    host: localhost
    port: 3306
    username: portfolio_user
    password: secure_password
    database: portfolio_db
    charset: utf8mb4
    collation: utf8mb4_unicode_ci
  max_open_conns: 100
  max_idle_conns: 10
  conn_max_lifetime: 3600
```

### Redis配置
```yaml
redis:
  host: localhost
  port: 6379
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5
```

## 表结构设计

### 1. 分类表 (categories)

**表名**: `categories`
**描述**: 存储作品分类信息

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | INT | - | NO | AUTO_INCREMENT | PRIMARY | 分类ID |
| name | VARCHAR | 100 | NO | - | - | 分类名称 |
| description | TEXT | - | YES | NULL | - | 分类描述 |
| sort_order | INT | - | NO | 0 | INDEX | 排序顺序 |
| created_at | TIMESTAMP | - | NO | CURRENT_TIMESTAMP | - | 创建时间 |

**SQL创建语句**:
```sql
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_sort_order (sort_order),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';
```

### 2. 作品表 (portfolios)

**表名**: `portfolios`
**描述**: 存储作品基本信息

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | INT | - | NO | AUTO_INCREMENT | PRIMARY | 作品ID |
| title | VARCHAR | 200 | NO | - | INDEX | 作品标题 |
| description | TEXT | - | YES | NULL | - | 作品描述 |
| category_id | INT | - | YES | NULL | FOREIGN KEY | 分类ID（可选） |
| cover_image | VARCHAR | 500 | NO | - | - | 封面图片路径 |
| sort_order | INT | - | NO | 0 | INDEX | 排序顺序 |
| status | ENUM | - | NO | 'published' | INDEX | 状态(draft/published) |
| created_at | TIMESTAMP | - | NO | CURRENT_TIMESTAMP | INDEX | 创建时间 |
| updated_at | TIMESTAMP | - | NO | CURRENT_TIMESTAMP ON UPDATE | - | 更新时间 |

**SQL创建语句**:
```sql
CREATE TABLE portfolios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '作品标题',
    description TEXT COMMENT '作品描述',
    category_id INT COMMENT '分类ID（可选）',
    cover_image VARCHAR(500) NOT NULL COMMENT '封面图片路径',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    status ENUM('draft', 'published') NOT NULL DEFAULT 'published' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order),
    INDEX idx_created_at (created_at),
    INDEX idx_title (title)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='作品表';
```

### 3. 作品图片表 (portfolio_images)

**表名**: `portfolio_images`
**描述**: 存储作品的详细图片信息

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | INT | - | NO | AUTO_INCREMENT | PRIMARY | 图片ID |
| portfolio_id | INT | - | NO | - | FOREIGN KEY | 作品ID |
| image_url | VARCHAR | 500 | NO | - | - | 图片URL |
| image_name | VARCHAR | 255 | YES | NULL | - | 图片名称 |
| sort_order | INT | - | NO | 0 | INDEX | 排序顺序 |
| created_at | TIMESTAMP | - | NO | CURRENT_TIMESTAMP | - | 创建时间 |

**SQL创建语句**:
```sql
CREATE TABLE portfolio_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    portfolio_id INT NOT NULL COMMENT '作品ID',
    image_url VARCHAR(500) NOT NULL COMMENT '图片URL',
    image_name VARCHAR(255) COMMENT '图片名称',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (portfolio_id) REFERENCES portfolios(id) ON DELETE CASCADE,
    INDEX idx_portfolio_id (portfolio_id),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='作品图片表';
```

## 数据库关系图

```
                     ┌──────────────┐       ┌─────────────────┐
                     │  categories  │       │   portfolios    │
                     ├──────────────┤       ├─────────────────┤
                     │ id (PK)      │◄──────┤ id (PK)         │
                     │ name         │       │ title           │
                     │ description  │       │ description     │
                     │ sort_order   │       │ category_id(FK) │
                     │ created_at   │       │ cover_image     │
                     └──────────────┘       │ sort_order      │
                                            │ status          │
                                            │ created_at      │
                                            │ updated_at      │
                                            └─────────────────┘
                                                     │
                                                     │ 1:N
                                                     ▼
                                            ┌─────────────────┐
                                            │portfolio_images │
                                            ├─────────────────┤
                                            │ id (PK)         │
                                            │ portfolio_id(FK)│
                                            │ image_url       │
                                            │ image_name      │
                                            │ sort_order      │
                                            │ created_at      │
                                            └─────────────────┘
```

## 索引设计

### 主要索引策略

1. **主键索引**: 每个表都有自增主键
2. **外键索引**: 所有外键字段都建立索引
3. **查询索引**: 根据常用查询条件建立索引
4. **复合索引**: 针对多字段查询建立复合索引
5. **全文索引**: 对标题和描述建立全文索引支持搜索

### 具体索引列表

#### portfolios表索引
```sql
-- 分类查询索引
CREATE INDEX idx_category_status ON portfolios(category_id, status);

-- 状态和排序复合索引
CREATE INDEX idx_status_sort ON portfolios(status, sort_order);

-- 创建时间和状态复合索引
CREATE INDEX idx_created_status ON portfolios(created_at DESC, status);

-- 浏览量排序索引
CREATE INDEX idx_view_count_desc ON portfolios(view_count DESC);
```

#### portfolio_images表索引
```sql
-- 作品图片查询索引
CREATE INDEX idx_portfolio_sort ON portfolio_images(portfolio_id, sort_order);
```

## 数据初始化

### 1. 创建默认分类
```sql
INSERT INTO categories (name, description, sort_order) VALUES 
('网页设计', '网页设计相关作品', 1),
('移动应用', '移动应用设计作品', 2),
('平面设计', '平面设计相关作品', 3),
('UI/UX设计', 'UI/UX设计作品', 4),
('插画设计', '插画和绘画作品', 5);
```

## 数据库优化

### 1. 查询优化

#### 常用查询语句优化
```sql
-- 获取已发布作品列表(分页)
SELECT p.*, c.name as category_name 
FROM portfolios p 
LEFT JOIN categories c ON p.category_id = c.id 
WHERE p.status = 'published' 
ORDER BY p.sort_order ASC, p.created_at DESC 
LIMIT 12 OFFSET 0;

-- 获取作品详情及图片
SELECT p.*, c.name as category_name,
       GROUP_CONCAT(
           CONCAT(pi.id, ':', pi.image_url, ':', pi.image_name, ':', pi.sort_order) 
           ORDER BY pi.sort_order 
           SEPARATOR '|'
       ) as images
FROM portfolios p 
LEFT JOIN categories c ON p.category_id = c.id 
LEFT JOIN portfolio_images pi ON p.id = pi.portfolio_id 
WHERE p.id = ? AND p.status = 'published'
GROUP BY p.id;
```

### 2. 分区策略

对于大量数据的情况，可以考虑按时间分区：

```sql
-- 按年份分区portfolios表
ALTER TABLE portfolios PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 3. 缓存策略

#### Redis缓存键设计
```
# 作品列表缓存
portfolio:list:{category_id}:{page}:{per_page}

# 作品详情缓存
portfolio:detail:{id}

# 分类列表缓存
categories:list

# 热门作品缓存
portfolio:hot:{limit}

# 用户会话缓存
session:{user_id}
```

#### 缓存过期时间
- 作品列表: 5分钟
- 作品详情: 30分钟
- 分类列表: 1小时
- 热门作品: 10分钟
- 用户会话: 24小时

## 数据备份策略

### 1. 定期备份
```bash
#!/bin/bash
# 每日备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"
DB_NAME="portfolio_db"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u backup_user -p$BACKUP_PASSWORD \
  --single-transaction \
  --routines \
  --triggers \
  $DB_NAME > $BACKUP_DIR/portfolio_db_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/portfolio_db_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
```

### 2. 增量备份
```bash
# 启用binlog
echo "log-bin=mysql-bin" >> /etc/mysql/mysql.conf.d/mysqld.cnf
echo "binlog-format=ROW" >> /etc/mysql/mysql.conf.d/mysqld.cnf
```

## 性能监控

### 1. 慢查询监控
```sql
-- 启用慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
SET GLOBAL log_queries_not_using_indexes = 'ON';
```

### 2. 性能指标监控
- 查询响应时间
- 连接数使用情况
- 缓存命中率
- 磁盘I/O使用率
- 内存使用情况

### 3. 监控SQL示例
```sql
-- 查看当前连接数
SHOW STATUS LIKE 'Threads_connected';

-- 查看查询缓存命中率
SHOW STATUS LIKE 'Qcache%';

-- 查看慢查询数量
SHOW STATUS LIKE 'Slow_queries';

-- 查看表锁等待
SHOW STATUS LIKE 'Table_locks_waited';
```

## 数据迁移

### 1. 版本升级脚本
```sql
-- v1.1.0 添加作品标签功能
CREATE TABLE portfolio_tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    portfolio_id INT NOT NULL,
    tag_name VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (portfolio_id) REFERENCES portfolios(id) ON DELETE CASCADE,
    INDEX idx_portfolio_id (portfolio_id),
    INDEX idx_tag_name (tag_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2. 数据迁移工具
```go
// 数据迁移接口
type Migration interface {
    Up() error
    Down() error
    Version() string
}

// 迁移管理器
type MigrationManager struct {
    db *gorm.DB
    migrations []Migration
}
```

这个数据库设计文档提供了完整的数据库架构，包括表结构、索引设计、优化策略和运维方案，为项目的数据存储提供了坚实的基础。
