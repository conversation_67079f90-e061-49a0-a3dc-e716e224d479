# 作品展示网站

> 🎨 一个简洁的作品展示网站，基于Go开发，专注于作品展示的核心功能

## 🚀 快速开始

### 第一步：下载和安装

```bash
# 1. 下载项目（或直接下载ZIP包解压）
git clone https://gitee.com/q0ne/portfolio-website.git
cd portfolio-website

# 2. 安装Go依赖
go mod download

# 3. 创建必要的目录
mkdir -p data uploads logs
```

### 第二步：配置

```bash
# 复制配置文件
cp configs/config.simple.yaml configs/config.yaml

# 修改管理员密码（重要！）
# 编辑 configs/config.yaml 文件，找到：
# admin:
#   password: admin123
# 改为你的安全密码
```

### 第三步：启动

```bash
# 启动网站
go run cmd/server/main.go
```

看到 "服务器启动在 :8080" 就成功了！

### 第四步：使用

1. **访问网站**：http://localhost:8080
2. **管理后台**：http://localhost:8080/admin
3. **默认账号**：admin / admin123（记得改密码）

#### 添加第一个作品
1. 打开 http://localhost:8080/admin
2. 用 admin/admin123 登录
3. 点击"添加作品"
4. 上传图片，填写标题和描述
5. 保存后前台就能看到了

## ✨ 主要功能

### 前台展示
- 📱 响应式作品展示，支持PC和手机
- 🖼️ 首页展示所有作品，网格布局
- 🔍 点击作品查看详情页的所有图片
- 🏷️ 可选的分类筛选功能

### 后台管理
- 🔐 简单的密码认证登录
- ➕ 添加作品功能（标题、描述、图片）
- ✏️ 编辑和删除作品
- 📁 可选的分类管理
- 🛡️ 自动的文件安全检查

## 🛠️ 技术栈

- **后端**: Go + Gin框架 + GORM
- **数据库**: SQLite（默认）或 MySQL
- **前端**: HTML + CSS + JavaScript
- **部署**: 支持Docker容器化

## 📁 项目结构

```
portfolio-website/
├── cmd/server/main.go          # 程序入口
├── internal/                   # 核心代码
├── web/                        # 前端页面和静态资源
└── configs/config.yaml         # 配置文件
```

## 🔧 常见问题

### 端口被占用了？
编辑 `configs/config.yaml`：
```yaml
server:
  port: 8081  # 改成其他端口
```

### 上传图片失败？
```bash
# 检查uploads目录权限
chmod 755 uploads/
```

### 忘记密码了？
编辑 `configs/config.yaml`，修改密码后重启程序。

### 想换成MySQL？
编辑 `configs/config.yaml`：
```yaml
database:
  driver: mysql
  mysql:
    host: localhost
    username: root
    password: your_password
    database: portfolio_db
```

## 🔒 安全提醒

1. **修改默认密码**（必须）
2. **设置文件权限**：
   ```bash
   chmod 640 configs/config.yaml
   chmod 755 uploads/
   ```
3. **生产环境**：
   - 使用HTTPS
   - 设置防火墙
   - 定期备份 `data/` 和 `uploads/` 目录

> 📖 **详细安全指南**: [安全配置文档](docs/SECURITY_SIMPLE.md)

## 数据库设计

### 分类表 (categories)
- id: 主键
- name: 分类名称
- description: 分类描述
- sort_order: 排序
- created_at: 创建时间

### 作品表 (portfolios)
- id: 主键
- title: 作品标题
- description: 作品描述
- category_id: 分类ID（可选）
- cover_image: 封面图片
- sort_order: 排序
- status: 状态(发布/草稿)
- created_at: 创建时间
- updated_at: 更新时间

### 作品图片表 (portfolio_images)
- id: 主键
- portfolio_id: 作品ID
- image_url: 图片URL
- image_name: 图片名称
- sort_order: 排序
- created_at: 创建时间

## API接口设计

### 前台接口
- `GET /api/portfolios` - 获取作品列表
- `GET /api/portfolios/{id}` - 获取作品详情
- `GET /api/categories` - 获取分类列表
- `GET /api/categories/{id}/portfolios` - 获取分类下的作品

### 后台接口
- `POST /api/admin/login` - 管理员登录
- `GET /api/admin/portfolios` - 获取作品列表(管理)
- `POST /api/admin/portfolios` - 创建作品
- `PUT /api/admin/portfolios/{id}` - 更新作品
- `DELETE /api/admin/portfolios/{id}` - 删除作品
- `POST /api/admin/upload` - 上传图片
- `GET /api/admin/categories` - 获取分类列表(管理)
- `POST /api/admin/categories` - 创建分类
- `PUT /api/admin/categories/{id}` - 更新分类
- `DELETE /api/admin/categories/{id}` - 删除分类

## 快速开始

### 环境要求
- Go 1.21+
- 数据库(二选一):
  - SQLite 3.0+ (推荐用于开发和小型部署)
  - MySQL 8.0+ (推荐用于生产环境)

## 📚 文档导航

### 使用指南
- 🔒 [基础安全配置](docs/SECURITY_SIMPLE.md) - 必要的安全设置
- 🐳 [部署文档](docs/DEPLOYMENT.md) - 生产环境部署

### 开发文档
- 🔧 [开发指南](docs/DEVELOPMENT.md) - 二次开发指南
- 📊 [API接口文档](docs/API.md) - RESTful API说明
- 🗄️ [数据库设计](docs/DATABASE.md) - 数据库架构
- 📋 [开发计划](docs/DEVELOPMENT_PLAN.md) - 完整开发计划

### 开源协议
MIT License - 可自由使用和修改

---

**🎉 开始创建你的个人作品展示网站吧！**
